@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 220 13% 91%;
    --foreground: 213 31% 18%;
    --card: 0 0% 100%;
    --card-foreground: 213 31% 18%;
    --popover: 0 0% 100%;
    --popover-foreground: 213 31% 18%;
    --primary: 213 94% 68%;
    --primary-foreground: 0 0% 100%;
    --secondary: 220 13% 91%;
    --secondary-foreground: 213 31% 18%;
    --muted: 220 13% 95%;
    --muted-foreground: 215 16% 47%;
    --accent: 213 94% 68%;
    --accent-foreground: 0 0% 100%;
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;
    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 213 94% 68%;
    --radius: 0.375rem;
    
    /* Custom theme colors */
    --tm-navy: 213 31% 18%;
    --tm-blue: 213 94% 68%;
    --tm-white: 0 0% 100%;
    --tm-gray: 220 13% 91%;
    --tm-dark-gray: 215 16% 47%;
  }

  .dark {
    --background: 213 31% 18%;
    --foreground: 0 0% 100%;
    --card: 213 30% 20%;
    --card-foreground: 0 0% 100%;
    --popover: 213 30% 20%;
    --popover-foreground: 0 0% 100%;
    --primary: 213 94% 68%;
    --primary-foreground: 213 31% 18%;
    --secondary: 213 30% 25%;
    --secondary-foreground: 0 0% 100%;
    --muted: 213 30% 25%;
    --muted-foreground: 215 20% 65%;
    --accent: 213 30% 25%;
    --accent-foreground: 0 0% 100%;
    --destructive: 0 62% 30%;
    --destructive-foreground: 0 0% 100%;
    --border: 213 30% 25%;
    --input: 213 30% 25%;
    --ring: 213 94% 68%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer utilities {
  .text-tm-navy { color: hsl(var(--tm-navy)); }
  .text-tm-blue { color: hsl(var(--tm-blue)); }
  .text-tm-white { color: hsl(var(--tm-white)); }
  .text-tm-gray { color: hsl(var(--tm-gray)); }
  .text-tm-dark-gray { color: hsl(var(--tm-dark-gray)); }
  .bg-tm-navy { background-color: hsl(var(--tm-navy)); }
  .bg-tm-blue { background-color: hsl(var(--tm-blue)); }
  .bg-tm-white { background-color: hsl(var(--tm-white)); }
  .bg-tm-gray { background-color: hsl(var(--tm-gray)); }
  .bg-tm-dark-gray { background-color: hsl(var(--tm-dark-gray)); }
}
