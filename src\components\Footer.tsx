import { Link } from "react-router-dom";
import { Mail, Phone, MapPin, Globe, Share2, Users } from "lucide-react";

const Footer = () => {
  return (
    <footer className="bg-tm-navy text-tm-white py-8 md:py-12 mt-8 md:mt-12">
      <div className="container mx-auto px-4">
        {/* Main Footer Content */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 md:gap-8 mb-8">
          {/* Club Info */}
          <div className="text-center md:text-left">
            <div className="flex items-center justify-center md:justify-start space-x-3 mb-4">
              <img
                src="/crown-fc-logo.svg"
                alt="Crown FC Logo"
                className="w-12 h-12"
              />
              <div>
                <h3 className="text-xl font-bold">Crown FC</h3>
                <p className="text-tm-blue text-sm">Ogbomoso</p>
              </div>
            </div>
            <p className="text-sm text-tm-gray leading-relaxed">
              Crown Football Club - Proudly representing Ogbomoso in the Premier League since our founding.
            </p>
          </div>

          {/* Quick Links */}
          <div className="text-center md:text-left">
            <h4 className="text-lg font-semibold mb-4">Quick Links</h4>
            <ul className="space-y-2 text-sm">
              <li><Link to="/" className="hover:text-tm-blue transition-colors">Home</Link></li>
              <li><Link to="/about" className="hover:text-tm-blue transition-colors">About Us</Link></li>
              <li><Link to="/news" className="hover:text-tm-blue transition-colors">News</Link></li>
              <li><Link to="/fixtures" className="hover:text-tm-blue transition-colors">Fixtures</Link></li>
              <li><Link to="/players" className="hover:text-tm-blue transition-colors">Players</Link></li>
            </ul>
          </div>

          {/* Contact Info */}
          <div className="text-center md:text-left">
            <h4 className="text-lg font-semibold mb-4">Contact Us</h4>
            <ul className="space-y-3 text-sm">
              <li className="flex items-center justify-center md:justify-start space-x-2">
                <MapPin size={16} className="text-tm-blue flex-shrink-0" />
                <span>Crown Stadium, Ogbomoso, Nigeria</span>
              </li>
              <li className="flex items-center justify-center md:justify-start space-x-2">
                <Phone size={16} className="text-tm-blue flex-shrink-0" />
                <span>+234 (0) ************</span>
              </li>
              <li className="flex items-center justify-center md:justify-start space-x-2">
                <Mail size={16} className="text-tm-blue flex-shrink-0" />
                <span><EMAIL></span>
              </li>
            </ul>
          </div>

          {/* Social Media */}
          <div className="text-center md:text-left">
            <h4 className="text-lg font-semibold mb-4">Follow Us</h4>
            <div className="flex justify-center md:justify-start space-x-4 mb-4">
              <a href="#" className="hover:text-tm-blue transition-colors" title="Facebook">
                <Globe size={20} />
              </a>
              <a href="#" className="hover:text-tm-blue transition-colors" title="Twitter">
                <Share2 size={20} />
              </a>
              <a href="#" className="hover:text-tm-blue transition-colors" title="Instagram">
                <Users size={20} />
              </a>
            </div>
            <p className="text-xs text-tm-gray">
              Stay updated with the latest news and match results
            </p>
          </div>
        </div>

        {/* Bottom Footer */}
        <div className="border-t border-tm-blue/30 pt-6">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <div className="text-center md:text-left">
              <p className="text-sm text-tm-gray">
                &copy; 2025 Crown Football Club. All rights reserved.
              </p>
            </div>
            <div className="flex flex-wrap justify-center md:justify-end space-x-6 text-sm">
              <Link to="/privacy" className="hover:text-tm-blue transition-colors">Privacy Policy</Link>
              <Link to="/terms" className="hover:text-tm-blue transition-colors">Terms of Service</Link>
              <Link to="/fan-zone" className="hover:text-tm-blue transition-colors">Fan Zone</Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
