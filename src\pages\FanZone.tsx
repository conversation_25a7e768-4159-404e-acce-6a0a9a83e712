import Header from "@/components/Header";
import Footer from "@/components/Footer";
import Breadcrumb from "@/components/Breadcrumb";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Facebook, Twitter, Instagram, Youtube, Mail, Phone, MapPin, Music, Heart, MessageCircle } from "lucide-react";

const FanZone = () => {
  return (
    <>
      <Header />
      <Breadcrumb 
        items={[
          { label: "Home", href: "/" },
          { label: "Fan Zone", href: "/fan-zone" }
        ]}
      />
      
      <div className="container mx-auto px-4 py-6">
        <div className="max-w-6xl mx-auto">
          <h1 className="text-3xl font-bold text-tm-navy mb-8">Fan Zone</h1>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Contact Form */}
            <Card>
              <CardHeader className="bg-tm-navy text-tm-white">
                <h2 className="font-bold flex items-center gap-2">
                  <MessageCircle className="h-5 w-5" />
                  Contact Us
                </h2>
              </CardHeader>
              <CardContent className="p-6">
                <form className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-tm-navy mb-2">
                        First Name
                      </label>
                      <Input placeholder="Enter your first name" />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-tm-navy mb-2">
                        Last Name
                      </label>
                      <Input placeholder="Enter your last name" />
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-tm-navy mb-2">
                      Email Address
                    </label>
                    <Input type="email" placeholder="Enter your email" />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-tm-navy mb-2">
                      Phone Number
                    </label>
                    <Input type="tel" placeholder="Enter your phone number" />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-tm-navy mb-2">
                      Subject
                    </label>
                    <Input placeholder="Enter message subject" />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-tm-navy mb-2">
                      Message
                    </label>
                    <Textarea placeholder="Enter your message" rows={4} />
                  </div>
                  <Button className="w-full bg-tm-blue hover:bg-tm-navy">
                    Send Message
                  </Button>
                </form>
              </CardContent>
            </Card>

            {/* Club Information */}
            <div className="space-y-6">
              {/* Contact Information */}
              <Card>
                <CardHeader className="bg-tm-blue text-tm-white">
                  <h2 className="font-bold">Contact Information</h2>
                </CardHeader>
                <CardContent className="p-6">
                  <div className="space-y-4">
                    <div className="flex items-center gap-3">
                      <MapPin className="h-5 w-5 text-tm-blue" />
                      <div>
                        <div className="font-medium text-tm-navy">Crown Stadium</div>
                        <div className="text-sm text-tm-dark-gray">123 Football Avenue, Sports City</div>
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      <Phone className="h-5 w-5 text-tm-blue" />
                      <div>
                        <div className="font-medium text-tm-navy">+****************</div>
                        <div className="text-sm text-tm-dark-gray">Main Office</div>
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      <Mail className="h-5 w-5 text-tm-blue" />
                      <div>
                        <div className="font-medium text-tm-navy"><EMAIL></div>
                        <div className="text-sm text-tm-dark-gray">General Inquiries</div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Social Media */}
              <Card>
                <CardHeader className="bg-tm-navy text-tm-white">
                  <h2 className="font-bold">Follow Us</h2>
                </CardHeader>
                <CardContent className="p-6">
                  <div className="grid grid-cols-2 gap-4">
                    <Button variant="outline" className="flex items-center gap-2 hover:bg-blue-50">
                      <Facebook className="h-4 w-4 text-blue-600" />
                      Facebook
                    </Button>
                    <Button variant="outline" className="flex items-center gap-2 hover:bg-blue-50">
                      <Twitter className="h-4 w-4 text-blue-400" />
                      Twitter
                    </Button>
                    <Button variant="outline" className="flex items-center gap-2 hover:bg-pink-50">
                      <Instagram className="h-4 w-4 text-pink-600" />
                      Instagram
                    </Button>
                    <Button variant="outline" className="flex items-center gap-2 hover:bg-red-50">
                      <Youtube className="h-4 w-4 text-red-600" />
                      YouTube
                    </Button>
                  </div>
                  <div className="mt-4 text-center">
                    <p className="text-sm text-tm-dark-gray">
                      Stay connected with Crown FC for latest updates, match highlights, and exclusive content!
                    </p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Club Anthem */}
          <Card className="mt-8">
            <CardHeader className="bg-tm-blue text-tm-white">
              <h2 className="font-bold flex items-center gap-2">
                <Music className="h-5 w-5" />
                Club Anthem
              </h2>
            </CardHeader>
            <CardContent className="p-6">
              <div className="text-center">
                <div className="bg-tm-gray rounded-lg p-8 mb-4">
                  <Music className="h-16 w-16 mx-auto mb-4 text-tm-blue" />
                  <h3 className="text-xl font-bold text-tm-navy mb-2">Crown FC Forever</h3>
                  <p className="text-tm-dark-gray mb-4">Our official club anthem</p>
                  <Button className="bg-tm-blue hover:bg-tm-navy">
                    <Music className="h-4 w-4 mr-2" />
                    Play Anthem
                  </Button>
                </div>
                <div className="max-w-2xl mx-auto">
                  <h4 className="font-bold text-tm-navy mb-4">Lyrics</h4>
                  <div className="text-tm-dark-gray space-y-2 text-sm">
                    <p><em>Rise up Crown FC, rise up strong</em></p>
                    <p><em>Together we stand, together we belong</em></p>
                    <p><em>Through victory and glory, through sun and rain</em></p>
                    <p><em>Crown FC forever, our pride will remain</em></p>
                    <p><em>Blue and white our colors, courage in our hearts</em></p>
                    <p><em>Champions on the field, united we'll never part</em></p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Fan Wall */}
          <Card className="mt-8">
            <CardHeader className="bg-tm-navy text-tm-white">
              <h2 className="font-bold flex items-center gap-2">
                <Heart className="h-5 w-5" />
                Fan Wall
              </h2>
            </CardHeader>
            <CardContent className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div className="bg-tm-gray rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <div className="w-8 h-8 bg-tm-blue rounded-full flex items-center justify-center">
                      <span className="text-white text-sm font-bold">JD</span>
                    </div>
                    <div>
                      <div className="font-medium text-tm-navy">John Doe</div>
                      <div className="text-xs text-tm-dark-gray">Fan since 2018</div>
                    </div>
                  </div>
                  <p className="text-sm text-tm-dark-gray">
                    "Crown FC is more than a team, it's a family! Love supporting our boys every match day!"
                  </p>
                  <div className="mt-2">
                    <Badge variant="outline" className="text-xs">
                      Season Ticket Holder
                    </Badge>
                  </div>
                </div>
                
                <div className="bg-tm-gray rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <div className="w-8 h-8 bg-tm-blue rounded-full flex items-center justify-center">
                      <span className="text-white text-sm font-bold">SJ</span>
                    </div>
                    <div>
                      <div className="font-medium text-tm-navy">Sarah Johnson</div>
                      <div className="text-xs text-tm-dark-gray">Fan since 2020</div>
                    </div>
                  </div>
                  <p className="text-sm text-tm-dark-gray">
                    "The atmosphere at Crown Stadium is incredible! Best fans in the league! 💙"
                  </p>
                  <div className="mt-2">
                    <Badge variant="outline" className="text-xs">
                      Youth Supporter
                    </Badge>
                  </div>
                </div>
                
                <div className="bg-tm-gray rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <div className="w-8 h-8 bg-tm-blue rounded-full flex items-center justify-center">
                      <span className="text-white text-sm font-bold">MB</span>
                    </div>
                    <div>
                      <div className="font-medium text-tm-navy">Mike Brown</div>
                      <div className="text-xs text-tm-dark-gray">Fan since 2015</div>
                    </div>
                  </div>
                  <p className="text-sm text-tm-dark-gray">
                    "Crown FC forever! Can't wait for the next home game. The team is looking strong this season!"
                  </p>
                  <div className="mt-2">
                    <Badge variant="outline" className="text-xs">
                      Founding Member
                    </Badge>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      <Footer />
    </>
  );
};

export default FanZone;