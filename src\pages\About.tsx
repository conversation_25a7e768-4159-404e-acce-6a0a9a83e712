import Header from "@/components/Header";
import Footer from "@/components/Footer";
import Breadcrumb from "@/components/Breadcrumb";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Trophy, Users, Target, Clock } from "lucide-react";

const About = () => {
  return (
    <>
      <Header />
      <Breadcrumb 
        items={[
          { label: "Home", href: "/" },
          { label: "About Crown FC", href: "/about" }
        ]}
      />
      
      <div className="container mx-auto px-4 py-6">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-3xl font-bold text-tm-navy mb-8">About Crown Football Club</h1>
          
          {/* Hero Section */}
          <div className="bg-tm-white rounded-lg shadow-sm p-8 mb-8">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
              <div>
                <h2 className="text-2xl font-bold text-tm-navy mb-4">Our Story</h2>
                <p className="text-tm-dark-gray mb-4">
                  Crown Football Club has been a cornerstone of football excellence, bringing passion, 
                  dedication, and community spirit to every match we play. Founded with the vision of 
                  developing talent and inspiring fans, we continue to strive for greatness.
                </p>
                <p className="text-tm-dark-gray">
                  From our humble beginnings to our current status, Crown FC represents the best of 
                  football culture - teamwork, perseverance, and the unwavering support of our fans.
                </p>
              </div>
              <div className="flex justify-center">
                <img 
                  src="/lovable-uploads/7b5265c1-2157-45c9-8a9f-96817a594778.png" 
                  alt="Crown FC Logo"
                  className="h-48 w-48"
                />
              </div>
            </div>
          </div>

          {/* Mission & Vision */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <Card>
              <CardHeader className="bg-tm-navy text-tm-white">
                <h3 className="font-bold flex items-center gap-2">
                  <Target className="h-5 w-5" />
                  Our Mission
                </h3>
              </CardHeader>
              <CardContent className="p-6">
                <p className="text-tm-dark-gray">
                  To develop exceptional football talent while fostering a strong sense of community 
                  and sportsmanship. We aim to inspire the next generation of players and fans through 
                  dedication, integrity, and excellence.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="bg-tm-blue text-tm-white">
                <h3 className="font-bold flex items-center gap-2">
                  <Trophy className="h-5 w-5" />
                  Our Vision
                </h3>
              </CardHeader>
              <CardContent className="p-6">
                <p className="text-tm-dark-gray">
                  To be recognized as a leading football club that not only achieves success on the field 
                  but also makes a positive impact in our community through youth development and social 
                  responsibility.
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Club Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
            <div className="bg-tm-gray rounded-lg p-6 text-center">
              <Clock className="h-8 w-8 mx-auto mb-2 text-tm-blue" />
              <div className="text-2xl font-bold text-tm-navy">Founded</div>
              <div className="text-tm-dark-gray">2015</div>
            </div>
            <div className="bg-tm-gray rounded-lg p-6 text-center">
              <Users className="h-8 w-8 mx-auto mb-2 text-tm-blue" />
              <div className="text-2xl font-bold text-tm-navy">Players</div>
              <div className="text-tm-dark-gray">25+</div>
            </div>
            <div className="bg-tm-gray rounded-lg p-6 text-center">
              <Trophy className="h-8 w-8 mx-auto mb-2 text-tm-blue" />
              <div className="text-2xl font-bold text-tm-navy">Trophies</div>
              <div className="text-tm-dark-gray">8</div>
            </div>
            <div className="bg-tm-gray rounded-lg p-6 text-center">
              <Target className="h-8 w-8 mx-auto mb-2 text-tm-blue" />
              <div className="text-2xl font-bold text-tm-navy">Goals</div>
              <div className="text-tm-dark-gray">Excellence</div>
            </div>
          </div>

          {/* Leadership */}
          <Card>
            <CardHeader className="bg-tm-navy text-tm-white">
              <h3 className="font-bold">Leadership Team</h3>
            </CardHeader>
            <CardContent className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center">
                  <div className="w-20 h-20 bg-tm-gray rounded-full mx-auto mb-4 flex items-center justify-center">
                    <Users className="h-8 w-8 text-tm-blue" />
                  </div>
                  <h4 className="font-bold text-tm-navy">John Smith</h4>
                  <p className="text-tm-dark-gray">Club President</p>
                </div>
                <div className="text-center">
                  <div className="w-20 h-20 bg-tm-gray rounded-full mx-auto mb-4 flex items-center justify-center">
                    <Users className="h-8 w-8 text-tm-blue" />
                  </div>
                  <h4 className="font-bold text-tm-navy">Mike Johnson</h4>
                  <p className="text-tm-dark-gray">Head Coach</p>
                </div>
                <div className="text-center">
                  <div className="w-20 h-20 bg-tm-gray rounded-full mx-auto mb-4 flex items-center justify-center">
                    <Users className="h-8 w-8 text-tm-blue" />
                  </div>
                  <h4 className="font-bold text-tm-navy">Sarah Williams</h4>
                  <p className="text-tm-dark-gray">General Manager</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      <Footer />
    </>
  );
};

export default About;