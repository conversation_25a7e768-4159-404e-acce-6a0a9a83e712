import { Link } from "react-router-dom";
import { ChevronRight } from "lucide-react";

interface BreadcrumbItem {
  label: string;
  href: string;
}

interface BreadcrumbProps {
  items?: BreadcrumbItem[];
}

const Breadcrumb = ({ items = [{ label: "Home", href: "/" }] }: BreadcrumbProps) => {
  return (
    <nav className="bg-tm-gray py-3">
      <div className="container mx-auto px-4">
        <div className="flex items-center space-x-2 text-sm">
          {items.map((item, index) => (
            <div key={index} className="flex items-center">
              {index > 0 && <ChevronRight className="h-4 w-4 mx-2 text-tm-dark-gray" />}
              <Link 
                to={item.href} 
                className={index === items.length - 1 ? "text-tm-dark-gray" : "text-tm-blue hover:underline"}
              >
                {item.label}
              </Link>
            </div>
          ))}
        </div>
      </div>
    </nav>
  );
};

export default Breadcrumb;
