import { Link } from "react-router-dom";

const Header = () => {
  return (
    <header className="bg-tm-navy text-tm-white">
      <div className="container mx-auto px-4 py-4">
        <nav className="flex items-center justify-between">
          <Link to="/" className="text-2xl font-bold">Crown FC</Link>
          <div className="flex space-x-6">
            <Link to="/" className="hover:text-tm-blue">Home</Link>
            <Link to="/about" className="hover:text-tm-blue">About</Link>
            <Link to="/news" className="hover:text-tm-blue">News</Link>
            <Link to="/fixtures" className="hover:text-tm-blue">Fixtures</Link>
            <Link to="/players" className="hover:text-tm-blue">Players</Link>
            <Link to="/gallery" className="hover:text-tm-blue">Gallery</Link>
            <Link to="/fan-zone" className="hover:text-tm-blue">Fan Zone</Link>
          </div>
        </nav>
      </div>
    </header>
  );
};

export default Header;
