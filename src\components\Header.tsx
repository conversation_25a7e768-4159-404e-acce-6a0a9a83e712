import { Link } from "react-router-dom";
import { useState } from "react";
import { Menu, X } from "lucide-react";

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const closeMenu = () => {
    setIsMenuOpen(false);
  };

  const navLinks = [
    { to: "/", label: "Home" },
    { to: "/about", label: "About" },
    { to: "/news", label: "News" },
    { to: "/fixtures", label: "Fixtures" },
    { to: "/players", label: "Players" },
    { to: "/gallery", label: "Gallery" },
    { to: "/fan-zone", label: "Fan Zone" },
  ];

  return (
    <header className="bg-tm-navy text-tm-white sticky top-0 z-50 shadow-lg">
      <div className="container mx-auto px-4">
        <nav className="flex items-center justify-between py-4">
          {/* Logo and Brand */}
          <Link to="/" className="flex items-center space-x-3 hover:opacity-80 transition-opacity">
            <img
              src="/crown-fc-logo.svg"
              alt="Crown FC Logo"
              className="w-10 h-10 md:w-12 md:h-12"
            />
            <div className="flex flex-col">
              <span className="text-xl md:text-2xl font-bold">Crown FC</span>
              <span className="text-xs md:text-sm text-tm-blue hidden sm:block">Ogbomoso</span>
            </div>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden lg:flex space-x-8">
            {navLinks.map((link) => (
              <Link
                key={link.to}
                to={link.to}
                className="hover:text-tm-blue transition-colors duration-200 font-medium"
              >
                {link.label}
              </Link>
            ))}
          </div>

          {/* Mobile Menu Button */}
          <button
            onClick={toggleMenu}
            className="lg:hidden p-2 rounded-md hover:bg-tm-blue/20 transition-colors"
            aria-label="Toggle menu"
          >
            {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
          </button>
        </nav>

        {/* Mobile Navigation Menu */}
        <div className={`lg:hidden transition-all duration-300 ease-in-out ${
          isMenuOpen
            ? 'max-h-96 opacity-100 pb-4'
            : 'max-h-0 opacity-0 overflow-hidden'
        }`}>
          <div className="border-t border-tm-blue/30 pt-4">
            <div className="flex flex-col space-y-3">
              {navLinks.map((link) => (
                <Link
                  key={link.to}
                  to={link.to}
                  onClick={closeMenu}
                  className="px-4 py-2 hover:bg-tm-blue/20 rounded-md transition-colors duration-200 font-medium"
                >
                  {link.label}
                </Link>
              ))}
            </div>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
