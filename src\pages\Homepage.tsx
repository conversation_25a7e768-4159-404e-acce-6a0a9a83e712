import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Calendar, Trophy, Users, Target, Clock, Play } from "lucide-react";

const Homepage = () => {
  const latestNews = [
    {
      id: 1,
      title: "Crown FC Secures Victory in Championship Match",
      excerpt: "Outstanding 3-1 victory against City United in yesterday's crucial match",
      date: "2025-01-15",
      category: "Match Report"
    },
    {
      id: 2,
      title: "New Signing Announcement",
      excerpt: "Welcome midfielder <PERSON> to the Crown FC family",
      date: "2025-01-14",
      category: "Transfer News"
    },
    {
      id: 3,
      title: "Youth Academy Training Camp",
      excerpt: "Special training camp for aspiring young players opens registration",
      date: "2025-01-13",
      category: "Academy News"
    }
  ];

  const upcomingMatches = [
    {
      id: 1,
      date: "2025-01-20",
      time: "15:00",
      opponent: "Thunder FC",
      venue: "Crown Stadium",
      isHome: true
    },
    {
      id: 2,
      date: "2025-01-27",
      time: "14:30",
      opponent: "City United",
      venue: "City Arena",
      isHome: false
    }
  ];

  return (
    <>
      <Header />
      
      <div className="container mx-auto px-4 py-6">
        {/* Hero Section */}
        <div className="bg-gradient-to-r from-tm-navy to-tm-blue rounded-lg text-tm-white p-8 mb-8">
          <div className="flex flex-col md:flex-row items-center justify-between">
            <div className="text-center md:text-left mb-6 md:mb-0">
              <h1 className="text-4xl font-bold mb-4">Welcome to Crown Football Club</h1>
              <p className="text-xl mb-6">Excellence, Passion, Victory</p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Button className="bg-tm-white text-tm-navy hover:bg-tm-gray">
                  View Latest Match
                </Button>
                <Button variant="outline" className="border-tm-white text-tm-white hover:bg-tm-white hover:text-tm-navy">
                  Join Fan Club
                </Button>
              </div>
            </div>
            <div className="text-center">
              <img 
                src="/lovable-uploads/7b5265c1-2157-45c9-8a9f-96817a594778.png" 
                alt="Crown FC Logo"
                className="h-32 w-32 mx-auto"
              />
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Latest News */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader className="bg-tm-navy text-tm-white">
                <h2 className="font-bold text-xl">Latest Club Updates</h2>
              </CardHeader>
              <CardContent className="p-6">
                <div className="space-y-6">
                  {latestNews.map((news) => (
                    <div key={news.id} className="border-b border-tm-gray pb-6 last:border-b-0">
                      <div className="flex items-start justify-between mb-2">
                        <Badge variant="outline" className="text-xs">
                          {news.category}
                        </Badge>
                        <span className="text-xs text-tm-dark-gray">
                          {new Date(news.date).toLocaleDateString()}
                        </span>
                      </div>
                      <h3 className="text-lg font-bold text-tm-navy mb-2 hover:text-tm-blue cursor-pointer">
                        {news.title}
                      </h3>
                      <p className="text-tm-dark-gray mb-3">
                        {news.excerpt}
                      </p>
                      <Button variant="ghost" size="sm" className="text-tm-blue hover:text-tm-navy p-0">
                        Read More →
                      </Button>
                    </div>
                  ))}
                </div>
                <div className="mt-6">
                  <Button className="w-full bg-tm-blue hover:bg-tm-navy">
                    View All News
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Upcoming Matches */}
            <Card>
              <CardHeader className="bg-tm-blue text-tm-white">
                <h2 className="font-bold">Next Matches</h2>
              </CardHeader>
              <CardContent className="p-4">
                <div className="space-y-4">
                  {upcomingMatches.map((match) => (
                    <div key={match.id} className="border border-tm-gray rounded-lg p-3">
                      <div className="text-center mb-2">
                        <div className="text-sm text-tm-dark-gray">
                          {new Date(match.date).toLocaleDateString()} - {match.time}
                        </div>
                      </div>
                      <div className="flex items-center justify-center gap-2 mb-2">
                        <span className="font-bold text-tm-navy">
                          {match.isHome ? "Crown FC" : match.opponent}
                        </span>
                        <span className="text-tm-dark-gray">vs</span>
                        <span className="font-bold text-tm-navy">
                          {match.isHome ? match.opponent : "Crown FC"}
                        </span>
                      </div>
                      <div className="text-center">
                        <Badge variant={match.isHome ? "default" : "outline"} className="text-xs">
                          {match.isHome ? "Home" : "Away"}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Quick Stats */}
            <Card>
              <CardHeader className="bg-tm-navy text-tm-white">
                <h2 className="font-bold">Season Stats</h2>
              </CardHeader>
              <CardContent className="p-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center">
                    <Trophy className="h-6 w-6 mx-auto mb-1 text-tm-blue" />
                    <div className="text-lg font-bold text-tm-navy">12</div>
                    <div className="text-xs text-tm-dark-gray">Wins</div>
                  </div>
                  <div className="text-center">
                    <Target className="h-6 w-6 mx-auto mb-1 text-tm-blue" />
                    <div className="text-lg font-bold text-tm-navy">45</div>
                    <div className="text-xs text-tm-dark-gray">Goals</div>
                  </div>
                  <div className="text-center">
                    <Users className="h-6 w-6 mx-auto mb-1 text-tm-blue" />
                    <div className="text-lg font-bold text-tm-navy">25</div>
                    <div className="text-xs text-tm-dark-gray">Players</div>
                  </div>
                  <div className="text-center">
                    <Clock className="h-6 w-6 mx-auto mb-1 text-tm-blue" />
                    <div className="text-lg font-bold text-tm-navy">2015</div>
                    <div className="text-xs text-tm-dark-gray">Founded</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Video Highlights */}
            <Card>
              <CardHeader className="bg-tm-blue text-tm-white">
                <h2 className="font-bold">Match Highlights</h2>
              </CardHeader>
              <CardContent className="p-4">
                <div className="bg-tm-gray rounded-lg h-32 flex items-center justify-center mb-3">
                  <Play className="h-12 w-12 text-tm-blue" />
                </div>
                <h3 className="font-bold text-tm-navy mb-2">Crown FC vs City United</h3>
                <p className="text-sm text-tm-dark-gray mb-3">
                  Best moments from our latest 3-1 victory
                </p>
                <Button variant="outline" size="sm" className="w-full">
                  Watch Now
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Club Announcements */}
        <Card className="mt-8">
          <CardHeader className="bg-tm-navy text-tm-white">
            <h2 className="font-bold text-xl">Club Announcements</h2>
          </CardHeader>
          <CardContent className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="bg-tm-light-blue rounded-lg p-6 mb-4">
                  <Calendar className="h-12 w-12 mx-auto text-tm-blue" />
                </div>
                <h3 className="font-bold text-tm-navy mb-2">Season Tickets</h3>
                <p className="text-sm text-tm-dark-gray">
                  Get your season tickets now and never miss a home game!
                </p>
              </div>
              <div className="text-center">
                <div className="bg-tm-light-blue rounded-lg p-6 mb-4">
                  <Users className="h-12 w-12 mx-auto text-tm-blue" />
                </div>
                <h3 className="font-bold text-tm-navy mb-2">Youth Academy</h3>
                <p className="text-sm text-tm-dark-gray">
                  Join our youth academy and develop your football skills with professional coaches.
                </p>
              </div>
              <div className="text-center">
                <div className="bg-tm-light-blue rounded-lg p-6 mb-4">
                  <Trophy className="h-12 w-12 mx-auto text-tm-blue" />
                </div>
                <h3 className="font-bold text-tm-navy mb-2">Fan Merchandise</h3>
                <p className="text-sm text-tm-dark-gray">
                  Show your support with official Crown FC jerseys and merchandise.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Footer />
    </>
  );
};

export default Homepage;