import Header from "@/components/Header";
import Breadcrumb from "@/components/Breadcrumb";
import Footer from "@/components/Footer";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Calendar, Clock, MapPin, Trophy } from "lucide-react";

const Fixtures = () => {
  const upcomingMatches = [
    {
      id: 1,
      date: "2025-01-20",
      time: "15:00",
      opponent: "Thunder FC",
      venue: "Crown Stadium",
      isHome: true,
      competition: "League Championship"
    },
    {
      id: 2,
      date: "2025-01-27",
      time: "14:30",
      opponent: "City United",
      venue: "City Arena",
      isHome: false,
      competition: "League Championship"
    },
    {
      id: 3,
      date: "2025-02-03",
      time: "16:00",
      opponent: "Eagles FC",
      venue: "Crown Stadium",
      isHome: true,
      competition: "Cup Quarter-Final"
    },
    {
      id: 4,
      date: "2025-02-10",
      time: "15:30",
      opponent: "Warriors FC",
      venue: "Warriors Ground",
      isHome: false,
      competition: "League Championship"
    }
  ];

  const recentResults = [
    {
      id: 1,
      date: "2025-01-15",
      opponent: "City United",
      homeScore: 3,
      awayScore: 1,
      venue: "Crown Stadium",
      isHome: true,
      competition: "League Championship"
    },
    {
      id: 2,
      date: "2025-01-08",
      opponent: "Lightning FC",
      homeScore: 1,
      awayScore: 2,
      venue: "Lightning Park",
      isHome: false,
      competition: "League Championship"
    },
    {
      id: 3,
      date: "2025-01-01",
      opponent: "Dragons FC",
      homeScore: 2,
      awayScore: 0,
      venue: "Crown Stadium",
      isHome: true,
      competition: "Cup Round 16"
    }
  ];

  return (
    <>
      <Header />
      <Breadcrumb 
        items={[
          { label: "Home", href: "/" },
          { label: "Fixtures & Results", href: "/fixtures" }
        ]}
      />
      
      <div className="container mx-auto px-4 py-6">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-3xl font-bold text-tm-navy mb-8">Fixtures & Results</h1>
          
          <Tabs defaultValue="upcoming" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="upcoming">Upcoming Fixtures</TabsTrigger>
              <TabsTrigger value="results">Recent Results</TabsTrigger>
            </TabsList>
            
            <TabsContent value="upcoming" className="space-y-4">
              <Card>
                <CardHeader className="bg-tm-navy text-tm-white">
                  <h2 className="font-bold flex items-center gap-2">
                    <Calendar className="h-5 w-5" />
                    Upcoming Matches
                  </h2>
                </CardHeader>
                <CardContent className="p-6">
                  <div className="space-y-4">
                    {upcomingMatches.map((match) => (
                      <div key={match.id} className="border border-tm-gray rounded-lg p-4 hover:shadow-md transition-shadow">
                        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-2">
                              <Badge variant="outline" className="text-xs">
                                {match.competition}
                              </Badge>
                              <div className="flex items-center gap-1 text-sm text-tm-dark-gray">
                                <Calendar className="h-4 w-4" />
                                <span>{new Date(match.date).toLocaleDateString()}</span>
                              </div>
                              <div className="flex items-center gap-1 text-sm text-tm-dark-gray">
                                <Clock className="h-4 w-4" />
                                <span>{match.time}</span>
                              </div>
                            </div>
                            <div className="flex items-center gap-4">
                              <div className="text-lg font-bold text-tm-navy">
                                {match.isHome ? "Crown FC" : match.opponent}
                              </div>
                              <div className="text-tm-dark-gray">vs</div>
                              <div className="text-lg font-bold text-tm-navy">
                                {match.isHome ? match.opponent : "Crown FC"}
                              </div>
                            </div>
                            <div className="flex items-center gap-1 mt-2 text-sm text-tm-dark-gray">
                              <MapPin className="h-4 w-4" />
                              <span>{match.venue}</span>
                              <Badge variant={match.isHome ? "secondary" : "outline"} className="ml-2">
                                {match.isHome ? "Home" : "Away"}
                              </Badge>
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="text-sm text-tm-dark-gray">Match in</div>
                            <div className="text-lg font-bold text-tm-blue">
                              {Math.ceil((new Date(match.date).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))} days
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
            
            <TabsContent value="results" className="space-y-4">
              <Card>
                <CardHeader className="bg-tm-blue text-tm-white">
                  <h2 className="font-bold flex items-center gap-2">
                    <Trophy className="h-5 w-5" />
                    Recent Results
                  </h2>
                </CardHeader>
                <CardContent className="p-6">
                  <div className="space-y-4">
                    {recentResults.map((result) => (
                      <div key={result.id} className="border border-tm-gray rounded-lg p-4">
                        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-2">
                              <Badge variant="outline" className="text-xs">
                                {result.competition}
                              </Badge>
                              <div className="flex items-center gap-1 text-sm text-tm-dark-gray">
                                <Calendar className="h-4 w-4" />
                                <span>{new Date(result.date).toLocaleDateString()}</span>
                              </div>
                            </div>
                            <div className="flex items-center gap-4">
                              <div className="text-lg font-bold text-tm-navy">
                                {result.isHome ? "Crown FC" : result.opponent}
                              </div>
                              <div className="text-2xl font-bold text-tm-blue">
                                {result.isHome ? result.homeScore : result.awayScore}
                              </div>
                              <div className="text-tm-dark-gray">-</div>
                              <div className="text-2xl font-bold text-tm-blue">
                                {result.isHome ? result.awayScore : result.homeScore}
                              </div>
                              <div className="text-lg font-bold text-tm-navy">
                                {result.isHome ? result.opponent : "Crown FC"}
                              </div>
                            </div>
                            <div className="flex items-center gap-1 mt-2 text-sm text-tm-dark-gray">
                              <MapPin className="h-4 w-4" />
                              <span>{result.venue}</span>
                              <Badge variant={result.isHome ? "secondary" : "outline"} className="ml-2">
                                {result.isHome ? "Home" : "Away"}
                              </Badge>
                            </div>
                          </div>
                          <div className="text-right">
                            <Badge variant={
                              (result.isHome && result.homeScore > result.awayScore) || 
                              (!result.isHome && result.awayScore > result.homeScore)
                                ? "default" 
                                : (result.homeScore === result.awayScore ? "secondary" : "destructive")
                            }>
                              {(result.isHome && result.homeScore > result.awayScore) || 
                               (!result.isHome && result.awayScore > result.homeScore)
                                ? "Win" 
                                : (result.homeScore === result.awayScore ? "Draw" : "Loss")}
                            </Badge>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>

      <Footer />
    </>
  );
};

export default Fixtures;
